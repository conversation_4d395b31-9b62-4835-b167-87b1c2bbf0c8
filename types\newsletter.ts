export interface NewsletterLanguage {
  id: string
  language: string
  language_display: string
  name: string
  updated_in_salesforce: string | null
  created_in_salesforce: boolean
  salesforce_id: string | null
}

export interface NewsletterUser {
  id: string
  first_name: string
  last_name: string
  email: string
  updated_at_platform: string
  created_at_platform: string
}

export interface Newsletter {
  id: string
  name: string
  brand: string
  brand_name: string
  template: string
  template_name: string
  status: string
  status_display: string
  languages: NewsletterLanguage[]
  created_by?: NewsletterUser
  updated_by?: NewsletterUser
  created_at?: string
  updated_at?: string
}

export interface NewsletterResponse {
  count: number
  next: string | null
  previous: string | null
  results: Newsletter[]
}

export interface NewsletterTableItem {
  id: string
  header: string
  type: string
  status: string
  target: string
  limit: string
  reviewer: string
}

// Newsletter creation types
export interface CreateNewsletterRequest {
  name: string
  template: string
  languages: string[]
  salesforce_folder?: string // Optional UUID of the selected Salesforce folder
}

export interface CreateNewsletterResponse {
  id: string
  name: string
  brand: string
  brand_name: string
  template: string
  template_name: string
  status: string
  status_display: string
  languages: NewsletterLanguage[]
  created_by?: NewsletterUser
  updated_by?: NewsletterUser
  created_at?: string
  updated_at?: string
}

// Newsletter Builder Types
export interface NewsletterBlockVariable {
  name: string
  variable_type: string
  values: {
    es: string
    ca: string
    fr: string
    en: string
  }
}

export interface NewsletterBlock {
  id: string
  name: string
  description: string
  html_content: string
  order_position: number
  is_visible: boolean
  variable_values: NewsletterBlockVariable[]
}

export interface NewsletterBuilderData {
  id: string
  name: string
  status: string
  brand: string
  brand_name: string
  nl_blocks: NewsletterBlock[]
  headers: NewsletterBlock[]
  footers: NewsletterBlock[]
}

export interface UpdateNewsletterRequest {
  newsletter_parent_id: string
  name: string
  status: string
  nl_blocks: NewsletterBlock[]
}
