"use client"

import * as React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  closestCenter,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core"
import { restrictToVerticalAxis } from "@dnd-kit/modifiers"
import {
  SortableContext,
  arrayMove,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { 
  ChevronDown, 
  ChevronRight, 
  GripVertical, 
  Bot, 
  Eye, 
  EyeOff,
  Plus
} from "lucide-react"
import { cn } from "@/lib/utils"
import { NewsletterBlock } from "@/types/newsletter"

interface BlockAccordionProps {
  blocks: NewsletterBlock[]
  onBlocksReorder: (blocks: NewsletterBlock[]) => void
  onBlockAIClick: (block: NewsletterBlock) => void
  onCreateBlock?: () => void
  brandName?: string
}

interface SortableBlockItemProps {
  block: NewsletterBlock
  isExpanded: boolean
  onToggle: () => void
  onAIClick: () => void
  isDragging?: boolean
}

function SortableBlockItem({ block, isExpanded, onToggle, onAIClick, isDragging }: SortableBlockItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: sortableIsDragging,
  } = useSortable({ id: block.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const isCurrentlyDragging = isDragging || sortableIsDragging

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "relative",
        isCurrentlyDragging && "z-10 opacity-80"
      )}
    >
      <Card className={cn("transition-colors", !block.is_visible && "opacity-60")}>
        <Collapsible open={isExpanded}>
          <CollapsibleTrigger asChild>
            <CardHeader 
              className="cursor-pointer hover:bg-muted/50 transition-colors"
              onClick={onToggle}
            >
              <div className="flex items-center gap-3">
                {/* Drag Handle */}
                <Button
                  {...attributes}
                  {...listeners}
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 cursor-grab active:cursor-grabbing"
                  onClick={(e) => e.stopPropagation()}
                >
                  <GripVertical className="h-4 w-4 text-muted-foreground" />
                </Button>

                {/* Expand/Collapse Icon */}
                <div className="flex-shrink-0">
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>

                {/* Block Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-sm truncate">{block.name}</CardTitle>
                    <div className="flex items-center gap-1">
                      {block.is_visible ? (
                        <Eye className="h-3 w-3 text-green-600" />
                      ) : (
                        <EyeOff className="h-3 w-3 text-muted-foreground" />
                      )}
                      <Badge variant="outline" className="text-xs">
                        #{block.order_position}
                      </Badge>
                    </div>
                  </div>
                  <CardDescription className="text-xs truncate">
                    {block.description || "No description"}
                  </CardDescription>
                </div>

                {/* AI Button */}
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-shrink-0"
                  onClick={(e) => {
                    e.stopPropagation()
                    onAIClick()
                  }}
                >
                  <Bot className="h-3 w-3 mr-1" />
                  AI
                </Button>
              </div>
            </CardHeader>
          </CollapsibleTrigger>

          <CollapsibleContent>
            <CardContent className="pt-0">
              <Separator className="mb-4" />
              
              {/* Block Details */}
              <div className="space-y-3">
                <div>
                  <h4 className="text-sm font-medium mb-2">Variables</h4>
                  {block.variable_values && block.variable_values.length > 0 ? (
                    <div className="space-y-2">
                      {block.variable_values.map((variable, index) => (
                        <div key={index} className="text-xs">
                          <div className="font-medium text-muted-foreground">
                            {variable.name} ({variable.variable_type})
                          </div>
                          <div className="grid grid-cols-2 gap-1 mt-1">
                            {Object.entries(variable.values).map(([lang, value]) => (
                              <div key={lang} className="truncate">
                                <span className="font-mono text-xs text-muted-foreground">
                                  {lang}:
                                </span>{" "}
                                <span className="text-xs">
                                  {value || "—"}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-xs text-muted-foreground">No variables defined</p>
                  )}
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">HTML Preview</h4>
                  <div className="bg-muted rounded p-2 text-xs font-mono max-h-20 overflow-y-auto">
                    {block.html_content ? (
                      <div className="whitespace-pre-wrap break-all">
                        {block.html_content.substring(0, 200)}
                        {block.html_content.length > 200 && "..."}
                      </div>
                    ) : (
                      <span className="text-muted-foreground">No HTML content</span>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  )
}

export function BlockAccordion({ 
  blocks, 
  onBlocksReorder, 
  onBlockAIClick, 
  onCreateBlock,
  brandName 
}: BlockAccordionProps) {
  const [expandedBlocks, setExpandedBlocks] = useState<Set<string>>(new Set())
  const [isDragging, setIsDragging] = useState(false)

  const sensors = useSensors(
    useSensor(MouseSensor, { activationConstraint: { distance: 5 } }),
    useSensor(TouchSensor),
    useSensor(KeyboardSensor)
  )

  const handleDragStart = () => {
    setIsDragging(true)
    // Collapse all blocks during drag
    setExpandedBlocks(new Set())
  }

  const handleDragEnd = (event: DragEndEvent) => {
    setIsDragging(false)

    const { active, over } = event
    if (!active || !over) return

    const activeId = active.id as string
    const overId = over.id as string

    if (activeId !== overId) {
      const oldIndex = blocks.findIndex(block => block.id === activeId)
      const newIndex = blocks.findIndex(block => block.id === overId)

      if (oldIndex !== -1 && newIndex !== -1) {
        try {
          const reorderedBlocks = arrayMove(blocks, oldIndex, newIndex)
          // Update order positions
          const updatedBlocks = reorderedBlocks.map((block, index) => ({
            ...block,
            order_position: index + 1
          }))
          onBlocksReorder(updatedBlocks)
        } catch (error) {
          console.error('Error reordering blocks:', error)
          // Reset drag state on error
          setIsDragging(false)
        }
      }
    }
  }

  const toggleBlockExpansion = (blockId: string) => {
    if (isDragging) return // Don't allow expansion during drag

    setExpandedBlocks(prev => {
      const newSet = new Set(prev)
      if (newSet.has(blockId)) {
        newSet.delete(blockId)
      } else {
        newSet.add(blockId)
      }
      return newSet
    })
  }

  const handleKeyboardToggle = (blockId: string, event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      toggleBlockExpansion(blockId)
    }
  }

  const sortedBlocks = (blocks || []).slice().sort((a, b) => a.order_position - b.order_position)

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Newsletter Blocks</h3>
          <p className="text-sm text-muted-foreground">
            {blocks.length} block{blocks.length !== 1 ? 's' : ''} 
            {brandName && ` for ${brandName}`}
          </p>
        </div>
        {onCreateBlock && (
          <Button variant="outline" size="sm" onClick={onCreateBlock}>
            <Plus className="h-4 w-4 mr-2" />
            New Block
          </Button>
        )}
      </div>

      {/* Blocks List */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        modifiers={[restrictToVerticalAxis]}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={sortedBlocks.map(block => block.id)} strategy={verticalListSortingStrategy}>
          <div className="space-y-2">
            {sortedBlocks.map((block) => (
              <SortableBlockItem
                key={block.id}
                block={block}
                isExpanded={expandedBlocks.has(block.id)}
                onToggle={() => toggleBlockExpansion(block.id)}
                onAIClick={() => onBlockAIClick(block)}
                isDragging={isDragging}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>

      {blocks.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No blocks found</p>
            {onCreateBlock && (
              <Button variant="outline" className="mt-4" onClick={onCreateBlock}>
                <Plus className="h-4 w-4 mr-2" />
                Create your first block
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
