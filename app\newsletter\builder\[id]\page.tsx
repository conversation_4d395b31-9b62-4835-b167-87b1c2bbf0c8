"use client"

import { useState } from 'react'
import { useParams } from 'next/navigation'
import { NextPage } from 'next'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Settings, Eye, Send, Bot, Save, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Spinner } from '@/components/ui/shadcn-io/spinner'
import Link from 'next/link'
import { toast } from 'sonner'

// Import our new components
import { useNewsletterBuilder } from '@/hooks/use-newsletter-builder'
import { BlockAccordion } from '@/components/newsletter/block-accordion'
import { HTMLPreview } from '@/components/newsletter/html-preview'
import { AIChatDrawer } from '@/components/newsletter/ai-chat-drawer'
import { NewsletterBlock } from '@/types/newsletter'

interface Props {}

const NewsletterBuilderPage: NextPage<Props> = ({}) => {
  const params = useParams()
  const newsletterId = params.id as string

  // State for AI drawer
  const [aiDrawerOpen, setAiDrawerOpen] = useState(false)
  const [selectedBlock, setSelectedBlock] = useState<NewsletterBlock | null>(null)
  const [aiDebugInfo, setAiDebugInfo] = useState<Record<string, any> | undefined>()

  // Newsletter data hook
  const {
    newsletterData,
    loading,
    error,
    refetch,
    updateNewsletter,
    updating,
    updateError
  } = useNewsletterBuilder({ newsletterId })

  const handleBlocksReorder = async (reorderedBlocks: NewsletterBlock[]) => {
    if (!newsletterData) return

    try {
      await updateNewsletter({
        newsletter_parent_id: newsletterId,
        name: newsletterData.name,
        status: newsletterData.status,
        nl_blocks: reorderedBlocks
      })

      toast.success('Blocks reordered successfully')
    } catch (err) {
      toast.error('Failed to reorder blocks')
      console.error('Error reordering blocks:', err)
    }
  }

  const handleBlockAIClick = (block: NewsletterBlock) => {
    setSelectedBlock(block)
    setAiDebugInfo({
      blockId: block.id,
      blockName: block.name,
      orderPosition: block.order_position,
      isVisible: block.is_visible,
      variableCount: block.variable_values?.length || 0
    })
    setAiDrawerOpen(true)
  }

  const handleGlobalAIClick = () => {
    setSelectedBlock(null)
    setAiDebugInfo({
      newsletterId,
      totalBlocks: newsletterData?.nl_blocks?.length || 0,
      visibleBlocks: newsletterData?.nl_blocks?.filter(b => b.is_visible)?.length || 0,
      brand: newsletterData?.brand_name
    })
    setAiDrawerOpen(true)
  }

  const handleCreateBlock = () => {
    // TODO: Implement create block functionality
    toast.info('Create block functionality coming soon')
  }

  const handleSave = async () => {
    if (!newsletterData) return

    try {
      await updateNewsletter({
        newsletter_parent_id: newsletterId,
        name: newsletterData.name,
        status: newsletterData.status,
        nl_blocks: newsletterData.nl_blocks
      })

      toast.success('Newsletter saved successfully')
    } catch (err) {
      toast.error('Failed to save newsletter')
      console.error('Error saving newsletter:', err)
    }
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Spinner className="h-8 w-8 mx-auto mb-4" />
            <p className="text-muted-foreground">Loading newsletter data...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
        <div className="flex justify-center">
          <Button onClick={refetch} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  if (!newsletterData) {
    return (
      <div className="p-6 space-y-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Newsletter not found
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/newsletters">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Newsletter Builder</h1>
            <p className="text-muted-foreground">
              {newsletterData.name} • {newsletterData.brand_name}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleSave}
            disabled={updating}
          >
            {updating ? (
              <Spinner className="h-4 w-4 mr-2" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save
          </Button>
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
          <Button size="sm">
            <Send className="h-4 w-4 mr-2" />
            Send
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {updateError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {updateError}
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content - Two Panel Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 min-h-[600px]">
        {/* Left Panel - Block Management */}
        <div className="space-y-4">
          <BlockAccordion
            blocks={newsletterData.nl_blocks || []}
            onBlocksReorder={handleBlocksReorder}
            onBlockAIClick={handleBlockAIClick}
            onCreateBlock={handleCreateBlock}
            brandName={newsletterData.brand_name}
          />
        </div>

        {/* Right Panel - HTML Preview */}
        <div className="lg:sticky lg:top-6 lg:h-[calc(100vh-8rem)]">
          <HTMLPreview
            blocks={newsletterData.nl_blocks || []}
            headers={newsletterData.headers || []}
            footers={newsletterData.footers || []}
            className="h-full"
          />
        </div>
      </div>

      {/* Global AI Button */}
      <Button
        className="fixed bottom-6 right-6 h-14 w-14 rounded-full shadow-lg"
        size="icon"
        onClick={handleGlobalAIClick}
      >
        <Bot className="h-6 w-6" />
      </Button>

      {/* AI Chat Drawer */}
      <AIChatDrawer
        open={aiDrawerOpen}
        onOpenChange={setAiDrawerOpen}
        block={selectedBlock}
        debugInfo={aiDebugInfo}
      />
    </div>
  )
}

export default NewsletterBuilderPage
