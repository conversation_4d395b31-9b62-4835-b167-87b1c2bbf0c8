"use client"

import { useParams } from 'next/navigation'
import { NextPage } from 'next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Settings, Eye, Send } from 'lucide-react'
import Link from 'next/link'

interface Props {}

const NewsletterBuilderPage: NextPage<Props> = ({}) => {
  const params = useParams()
  const newsletterId = params.id as string

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Tornar
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Constructor de Newsletter</h1>
            <p className="text-muted-foreground">
              ID de la Newsletter: {newsletterId}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4 mr-2" />
            Previsualitzar
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Configuració
          </Button>
          <Button size="sm">
            <Send className="h-4 w-4 mr-2" />
            Enviar
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sidebar - Tools */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Eines</CardTitle>
              <CardDescription>
                Afegeix i edita elements de la newsletter
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  Afegir Text
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  Afegir Imatge
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  Afegir Botó
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  Afegir Separador
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Editor Area */}
        <div className="lg:col-span-2">
          <Card className="min-h-[600px]">
            <CardHeader>
              <CardTitle>Editor de Newsletter</CardTitle>
              <CardDescription>
                Construeix el contingut de la teva newsletter aquí
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center min-h-[500px] flex items-center justify-center">
                <div className="space-y-4">
                  <div className="text-muted-foreground">
                    <Settings className="h-12 w-12 mx-auto mb-4" />
                  </div>
                  <h3 className="text-lg font-medium">Constructor en desenvolupament</h3>
                  <p className="text-muted-foreground max-w-md">
                    El constructor de newsletters està en desenvolupament. 
                    Aviat podràs crear i editar el contingut de les teves newsletters aquí.
                  </p>
                  <div className="pt-4">
                    <Link href="/">
                      <Button variant="outline">
                        Tornar a l'inici
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default NewsletterBuilderPage
