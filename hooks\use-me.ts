import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { UserSessionInfo } from '@/types/user'
import useUserStore from '@/store/use-user-store'
import { DEFAULT_API_URL } from '@/constants/constants'

interface UseMeReturn {
  user: UserSessionInfo | null
  loading: boolean
  error: string | null
  isRefreshing: boolean
  refetch: () => void
}

export function useMe(): UseMeReturn {
  const { data: session } = useSession()
  const { user, setUser, isRefreshing, refreshToken } = useUserStore()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

  const getAuthHeaders = useCallback((accessToken?: string) => {
    const token = accessToken || session?.djangoAccessToken || user?.session?.accessToken
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    }
  }, [session?.djangoAccessToken, user?.session?.accessToken])

  const fetchMe = useCallback(async (accessToken?: string) => {
    const token = accessToken || session?.djangoAccessToken || user?.session?.accessToken

    if (!token) {
      setError('Authentication required')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`${API_BASE_URL}/users/me/`, {
        method: 'GET',
        headers: getAuthHeaders(token),
      })

      if (!response.ok) {
        // If token is expired and we have a refresh token, try to refresh
        if (response.status === 401 && user?.session?.refreshToken) {
          console.log('🔄 Access token expired, attempting refresh...')
          const refreshSuccess = await refreshToken(user.session.refreshToken)

          if (refreshSuccess) {
            // Retry the request with the new token
            const updatedUser = useUserStore.getState().user
            if (updatedUser?.session?.accessToken) {
              return fetchMe(updatedUser.session.accessToken)
            }
          }
        }

        const errorMessage = await extractApiErrorMessage(response, 'Failed to fetch user')
        throw new Error(errorMessage)
      }

      const data: UserSessionInfo = await response.json()

      // Merge with existing session data if available
      const mergedData: UserSessionInfo = {
        ...data,
        session: {
          ...data.session,
          // Preserve existing session tokens if not provided in response
          accessToken: data.session?.accessToken || user?.session?.accessToken || session?.djangoAccessToken || '',
          refreshToken: data.session?.refreshToken || user?.session?.refreshToken || session?.djangoRefreshToken || '',
        }
      }

      setUser(mergedData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setUser(null)
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, session?.djangoRefreshToken, getAuthHeaders, API_BASE_URL, user, setUser, refreshToken])

  // Initial fetch when session is available
  useEffect(() => {
    if (session?.djangoAccessToken && !user) {
      fetchMe()
    }
  }, [session?.djangoAccessToken, user, fetchMe])

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      useUserStore.getState().clearRefreshTimer()
    }
  }, [])

  const refetch = useCallback(() => {
    fetchMe()
  }, [fetchMe])

  return {
    user,
    loading,
    error,
    isRefreshing,
    refetch
  }
}