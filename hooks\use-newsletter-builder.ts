import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { NewsletterBuilderData, UpdateNewsletterRequest } from '@/types/newsletter'
import { DEFAULT_API_URL } from '@/constants/constants'
import { extractApiErrorMessage } from '@/lib/api-error-utils'

interface UseNewsletterBuilderParams {
  newsletterId: string
}

interface UseNewsletterBuilderReturn {
  newsletterData: NewsletterBuilderData | null
  loading: boolean
  error: string | null
  refetch: () => void
  updateNewsletter: (data: UpdateNewsletterRequest) => Promise<void>
  updating: boolean
  updateError: string | null
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

export function useNewsletterBuilder({ newsletterId }: UseNewsletterBuilderParams): UseNewsletterBuilderReturn {
  const { data: session, status } = useSession()
  const [newsletterData, setNewsletterData] = useState<NewsletterBuilderData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [updating, setUpdating] = useState(false)
  const [updateError, setUpdateError] = useState<string | null>(null)

  const getAuthHeaders = useCallback(() => {
    if (!session?.djangoAccessToken) {
      throw new Error('No authentication token available')
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.djangoAccessToken}`
    }
  }, [session?.djangoAccessToken])

  const fetchNewsletterData = useCallback(async () => {
    if (!newsletterId) {
      setError('Newsletter ID is required')
      setLoading(false)
      return
    }

    if (!session?.djangoAccessToken) {
      setError('Authentication required')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const headers = getAuthHeaders()
      const url = `${API_BASE_URL}/builder/newsletter-blocks?newsletter_parent_id=${newsletterId}`

      console.log('Fetching newsletter data from:', url)
      console.log('Headers:', headers)

      const response = await fetch(url, {
        method: 'GET',
        headers,
      })

      console.log('Response status:', response.status)
      console.log('Response ok:', response.ok)

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to fetch newsletter data')
        console.error('API Error:', errorMessage)

        // For development: provide mock data if API endpoint doesn't exist
        if (response.status === 404) {
          console.warn('API endpoint not found, using mock data for development')
          const mockData: NewsletterBuilderData = {
            id: newsletterId,
            name: 'Sample Newsletter',
            status: 'draft',
            brand: 'sample-brand',
            brand_name: 'Sample Brand',
            nl_blocks: [
              {
                id: 'block-1',
                name: 'Header Block',
                description: 'Newsletter header with logo and title',
                html_content: '<div style="text-align: center; padding: 20px;"><h1>{{title}}</h1><p>{{subtitle}}</p></div>',
                order_position: 1,
                is_visible: true,
                variable_values: [
                  {
                    name: 'title',
                    variable_type: 'text_paragraf',
                    values: {
                      es: 'Título del Newsletter',
                      ca: 'Títol del Newsletter',
                      fr: 'Titre de la Newsletter',
                      en: 'Newsletter Title'
                    }
                  },
                  {
                    name: 'subtitle',
                    variable_type: 'text_paragraf',
                    values: {
                      es: 'Subtítulo descriptivo',
                      ca: 'Subtítol descriptiu',
                      fr: 'Sous-titre descriptif',
                      en: 'Descriptive subtitle'
                    }
                  }
                ]
              },
              {
                id: 'block-2',
                name: 'Content Block',
                description: 'Main content area',
                html_content: '<div style="padding: 20px;"><h2>{{content_title}}</h2><p>{{content_text}}</p></div>',
                order_position: 2,
                is_visible: true,
                variable_values: [
                  {
                    name: 'content_title',
                    variable_type: 'text_paragraf',
                    values: {
                      es: 'Contenido Principal',
                      ca: 'Contingut Principal',
                      fr: 'Contenu Principal',
                      en: 'Main Content'
                    }
                  },
                  {
                    name: 'content_text',
                    variable_type: 'text_paragraf',
                    values: {
                      es: 'Este es el contenido principal del newsletter.',
                      ca: 'Aquest és el contingut principal del newsletter.',
                      fr: 'Ceci est le contenu principal de la newsletter.',
                      en: 'This is the main content of the newsletter.'
                    }
                  }
                ]
              }
            ],
            headers: [],
            footers: []
          }
          setNewsletterData(mockData)
          return
        }

        throw new Error(errorMessage)
      }

      const data: NewsletterBuilderData = await response.json()
      console.log('Newsletter data received:', data)
      setNewsletterData(data)
    } catch (err) {
      console.error('Error in fetchNewsletterData:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
      setNewsletterData(null)
    } finally {
      setLoading(false)
    }
  }, [newsletterId, session?.djangoAccessToken, getAuthHeaders])

  const updateNewsletter = useCallback(async (data: UpdateNewsletterRequest) => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required')
    }

    try {
      setUpdating(true)
      setUpdateError(null)

      const response = await fetch(`${API_BASE_URL}/builder/update-newsletter/`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to update newsletter')
        throw new Error(errorMessage)
      }

      // Refetch data after successful update
      await fetchNewsletterData()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while updating newsletter'
      setUpdateError(errorMessage)
      throw err
    } finally {
      setUpdating(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders, fetchNewsletterData])

  useEffect(() => {
    // Only fetch data when session is loaded and we have a token
    if (status === 'loading') {
      return // Wait for session to load
    }

    if (status === 'unauthenticated') {
      setError('Authentication required')
      setLoading(false)
      return
    }

    fetchNewsletterData()
  }, [fetchNewsletterData, status])

  return {
    newsletterData,
    loading,
    error,
    refetch: fetchNewsletterData,
    updateNewsletter,
    updating,
    updateError
  }
}
