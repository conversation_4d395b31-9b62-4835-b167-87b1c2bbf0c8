"use client"

import * as React from "react"
import { useState, useEffect, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Eye, Copy, Check, Globe } from "lucide-react"
import { cn } from "@/lib/utils"
import { NewsletterBlock } from "@/types/newsletter"
import { useLanguages } from "@/hooks/use-languages"

interface HTMLPreviewProps {
  blocks: NewsletterBlock[]
  headers?: NewsletterBlock[]
  footers?: NewsletterBlock[]
  className?: string
}

export function HTMLPreview({ blocks, headers = [], footers = [], className }: HTMLPreviewProps) {
  const { languages, loading: languagesLoading } = useLanguages()
  const [selectedLanguage, setSelectedLanguage] = useState("es")
  const [copied, setCopied] = useState(false)

  // Build the complete HTML preview
  const previewHtml = useMemo(() => {
    const safeHeaders = headers || []
    const safeBlocks = blocks || []
    const safeFooters = footers || []

    const allBlocks = [
      ...safeHeaders.map(h => ({ ...h, type: 'header' })),
      ...safeBlocks.filter(b => b?.is_visible).sort((a, b) => (a?.order_position || 0) - (b?.order_position || 0)),
      ...safeFooters.map(f => ({ ...f, type: 'footer' }))
    ]

    let html = ""
    
    for (const block of allBlocks) {
      let content = block.html_content || ""
      
      // Replace variables with their values in the selected language
      if (block.variable_values && Array.isArray(block.variable_values)) {
        block.variable_values.forEach((variable) => {
          const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
          const languageValue = variable.values[selectedLanguage as keyof typeof variable.values] || ''
          content = content.replace(regex, languageValue)
        })
      }

      // Add block comment for debugging
      const blockType = (block as any).type || 'block'
      html += `\n<!-- ${blockType}: ${block.name} (#${block.order_position}) -->\n`
      html += content + "\n"
    }

    return html
  }, [blocks, headers, footers, selectedLanguage])

  // Get visible blocks count
  const visibleBlocksCount = (blocks || []).filter(b => b?.is_visible).length
  const totalBlocksCount = (blocks || []).length

  const handleCopyHtml = async () => {
    try {
      await navigator.clipboard.writeText(previewHtml)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy HTML:', err)
      // Fallback for browsers that don't support clipboard API
      try {
        const textArea = document.createElement('textarea')
        textArea.value = previewHtml
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      } catch (fallbackErr) {
        console.error('Fallback copy failed:', fallbackErr)
      }
    }
  }

  // Set default language when languages load
  useEffect(() => {
    if (languages.length > 0 && !selectedLanguage) {
      setSelectedLanguage(languages[0].language)
    }
  }, [languages, selectedLanguage])

  return (
    <Card className={cn("h-full flex flex-col", className)}>
      <CardHeader className="flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            <CardTitle>Live Preview</CardTitle>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Language Selector */}
            <Select 
              value={selectedLanguage} 
              onValueChange={setSelectedLanguage}
              disabled={languagesLoading}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Language">
                  <div className="flex items-center gap-2">
                    <Globe className="h-3 w-3" />
                    {selectedLanguage.toUpperCase()}
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {languages.map((language) => (
                  <SelectItem key={language.language} value={language.language}>
                    <div className="flex items-center gap-2">
                      <Globe className="h-3 w-3" />
                      {language.language_display}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Copy HTML Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyHtml}
              disabled={!previewHtml.trim()}
            >
              {copied ? (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy HTML
                </>
              )}
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <CardDescription>
            Real-time preview of your newsletter
          </CardDescription>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {visibleBlocksCount}/{totalBlocksCount} blocks visible
            </Badge>
            {headers.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {headers.length} header{headers.length !== 1 ? 's' : ''}
              </Badge>
            )}
            {footers.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {footers.length} footer{footers.length !== 1 ? 's' : ''}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <Separator />

      <CardContent className="flex-1 p-0 overflow-hidden">
        <div className="h-full overflow-y-auto">
          {previewHtml ? (
            <div className="p-6">
              {/* Preview Container */}
              <div 
                className="border rounded-lg p-6 bg-white shadow-inner min-h-[400px]"
                style={{ 
                  maxWidth: '640px', 
                  margin: '0 auto',
                  fontFamily: 'Arial, sans-serif'
                }}
              >
                <div 
                  dangerouslySetInnerHTML={{ __html: previewHtml }}
                  className="newsletter-preview"
                />
              </div>

              {/* HTML Source (collapsible) */}
              <details className="mt-6">
                <summary className="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground">
                  View HTML Source
                </summary>
                <div className="mt-2 p-4 bg-muted rounded-lg">
                  <pre className="text-xs overflow-x-auto whitespace-pre-wrap break-all">
                    <code>{previewHtml}</code>
                  </pre>
                </div>
              </details>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground p-6">
              <div className="text-center max-w-md">
                <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">No Content to Preview</h3>
                <p className="text-sm">
                  Add some blocks to your newsletter to see the live preview here.
                  The preview will update automatically as you make changes.
                </p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
