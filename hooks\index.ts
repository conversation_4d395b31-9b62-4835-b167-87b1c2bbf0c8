// Export all hooks for easier importing
export { useNewsletters } from './use-newsletters'
export { useCreateNewsletter } from './use-create-newsletter'
export { useSalesforcefolders } from './use-salesforce-folders'
export { useCreateSalesforceFolder } from './use-create-salesforce-folder'
export { usePermissions } from './use-permissions'
export { useRoles } from './use-roles'
export { useUsers } from './use-users'
export { useBlocks } from './use-blocks'
export { useBrands } from './use-brands'
export { useLanguages } from './use-languages'
export { useCreateBlock } from './use-create-block'
export { useVariableTypes } from './use-variable-types'
export { useHeadersFooters } from './use-headers-footers'
export { useCreateHeaderFooter } from './use-create-header-footer'
export { useUpdateHeaderFooter } from './use-update-header-footer'
export { useDeleteHeaderFooter } from './use-delete-header-footer'
// export { useMobile } from './use-mobile'

// Re-export types for convenience
export type { Permission, Role } from '@/types/role'
export type { User, CreateUserRequest, UpdateUserRequest, UIUser } from '@/types/user'
export type { Newsletter, NewsletterResponse, CreateNewsletterRequest, CreateNewsletterResponse } from '@/types/newsletter'
export type { SalesforceFolder, SalesforceFoldersResponse, CreateSalesforceFolderRequest, CreateSalesforceFolderResponse, FlatSalesforceFolder } from '@/types/salesforce'
export type { Brand } from './use-brands'
export type { Language } from './use-languages'
export type { VariableType } from './use-variable-types'
export type { CreateHeaderFooterRequest } from './use-create-header-footer'
export type { UpdateHeaderFooterRequest } from './use-update-header-footer'
